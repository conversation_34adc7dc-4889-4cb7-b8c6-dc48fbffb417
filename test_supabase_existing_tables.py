#!/usr/bin/env python3
"""
Test Real Supabase API with Existing Tables
This script tests the actual Supabase API connection using existing tables
"""

import asyncio
import logging
from app.services.mcp.real_supabase_service import RealSupabaseService
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_supabase_existing_tables():
    """Test real Supabase API with existing tables"""
    print("=" * 60)
    print("TESTING REAL SUPABASE API WITH EXISTING TABLES")
    print("=" * 60)
    
    try:
        # Create Supabase service
        supabase_service = RealSupabaseService()
        print("✓ Supabase service created")
        
        # Connect to Supabase
        await supabase_service.connect()
        print("✓ Connected to Supabase")
        
        # Test 1: Store portfolio metrics (existing table)
        print("\n--- Test 1: Store Portfolio Metrics ---")
        portfolio_data = {
            'portfolio_value': 105000.0,
            'total_return': 5.0,
            'sharpe_ratio': 1.85,
            'max_drawdown': -2.3,
            'win_rate': 0.68,
            'symbol': 'PORTFOLIO',
            'timestamp': datetime.now().isoformat(),
            'metadata': {
                'test': True,
                'integration': 'real_api',
                'strategy': 'ensemble'
            }
        }
        
        result = await supabase_service.store_portfolio_metrics(portfolio_data)
        if result:
            print(f"✓ Portfolio metrics stored successfully")
            print(f"  Portfolio value: ${portfolio_data['portfolio_value']:,.2f}")
            print(f"  Total return: {portfolio_data['total_return']:.2f}%")
            print(f"  Sharpe ratio: {portfolio_data['sharpe_ratio']:.2f}")
        
        # Test 2: Query portfolio metrics
        print("\n--- Test 2: Query Portfolio Metrics ---")
        async with supabase_service.session.get(
            f"{supabase_service.api_url}/portfolio_metrics?select=*&limit=5&order=created_at.desc",
            headers=supabase_service.headers
        ) as response:
            if response.status == 200:
                records = await response.json()
                print(f"✓ Retrieved {len(records)} portfolio records")
                if records:
                    latest = records[0]
                    print(f"  Latest portfolio value: ${float(latest.get('portfolio_value', 0)):,.2f}")
                    print(f"  Latest return: {float(latest.get('total_return', 0)):.2f}%")
        
        # Test 3: Store strategy performance (existing table)
        print("\n--- Test 3: Store Strategy Performance ---")
        strategy_data = {
            'strategy_name': 'real_api_test_strategy',
            'symbol': 'BTCUSDT',
            'total_trades': 45,
            'winning_trades': 31,
            'win_rate': 0.689,
            'total_return': 0.052,
            'sharpe_ratio': 1.85,
            'max_drawdown': -0.023,
            'profit_factor': 1.45,
            'avg_win': 0.012,
            'avg_loss': -0.008,
            'confidence_score': 0.87,
            'timestamp': datetime.now().isoformat(),
            'metadata': {
                'test': True,
                'api_integration': True,
                'version': '1.0.0'
            }
        }
        
        # Store strategy performance
        async with supabase_service.session.post(
            f"{supabase_service.api_url}/strategy_performance",
            headers=supabase_service.headers,
            json=strategy_data
        ) as response:
            if response.status in [200, 201]:
                print("✓ Strategy performance stored successfully")
                print(f"  Strategy: {strategy_data['strategy_name']}")
                print(f"  Win rate: {strategy_data['win_rate']:.1%}")
                print(f"  Total return: {strategy_data['total_return']:.1%}")
                print(f"  Sharpe ratio: {strategy_data['sharpe_ratio']:.2f}")
            else:
                error_text = await response.text()
                print(f"✗ Failed to store strategy performance: {response.status} - {error_text}")
        
        # Test 4: Query strategy performance
        print("\n--- Test 4: Query Strategy Performance ---")
        async with supabase_service.session.get(
            f"{supabase_service.api_url}/strategy_performance?select=*&limit=5&order=created_at.desc",
            headers=supabase_service.headers
        ) as response:
            if response.status == 200:
                records = await response.json()
                print(f"✓ Retrieved {len(records)} strategy records")
                if records:
                    latest = records[0]
                    print(f"  Latest strategy: {latest.get('strategy_name', 'Unknown')}")
                    print(f"  Win rate: {float(latest.get('win_rate', 0)):.1%}")
                    print(f"  Return: {float(latest.get('total_return', 0)):.1%}")
        
        # Test 5: Test connection status
        print("\n--- Test 5: Connection Status ---")
        status = supabase_service.get_connection_status()
        print(f"✓ Connection status: {'Connected' if status['connected'] else 'Disconnected'}")
        print(f"  Supabase URL: {status['supabase_url']}")
        print(f"  Error count: {status['error_count']}")
        
        # Test 6: Performance stats
        print("\n--- Test 6: Performance Statistics ---")
        stats = supabase_service.get_performance_stats()
        print(f"✓ Performance stats:")
        print(f"  Total operations: {stats['total_operations']}")
        print(f"  Store calls: {stats['store_calls']}")
        print(f"  Query calls: {stats['query_calls']}")
        if stats['total_operations'] > 0:
            print(f"  Average latency: {stats['avg_latency']:.2f}ms")
            print(f"  Max latency: {stats['max_latency']:.2f}ms")
            print(f"  Min latency: {stats['min_latency']:.2f}ms")
        
        print("\n" + "=" * 60)
        print("✅ ALL SUPABASE TESTS PASSED")
        print("Real Supabase API is working with existing tables!")
        print("Portfolio and strategy data storage is operational.")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Supabase test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up connection
        try:
            if supabase_service:
                await supabase_service.disconnect()
                print("\n✓ Supabase connection closed")
        except Exception as e:
            print(f"\n⚠️  Error closing Supabase connection: {e}")

if __name__ == "__main__":
    success = asyncio.run(test_supabase_existing_tables())
    exit(0 if success else 1)