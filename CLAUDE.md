# Claude AI Assistant Guidelines

## Python Virtual Environment Usage

**CRITICAL REQUIREMENT**: When accessing or installing any Python package, the virtual environment must always be activated first in the same command line using `source venv/bin/activate &&`.

Examples:
- `source venv/bin/activate && pip install package_name`
- `source venv/bin/activate && python script.py`
- `source venv/bin/activate && pytest tests/`
- `source venv/bin/activate && uv add package_name`

This ensures all Python operations use the project's isolated environment and prevents conflicts with system packages.

## Command Execution Rules

**CRITICAL REQUIREMENT**: Always run commands from the project directory unless absolutely necessary to run from elsewhere.

### Directory Management Protocol
1. **Default Location**: All commands should be executed from `/mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/`
2. **Error Handling**: When encountering "No such file or directory" errors:
   - First navigate back to the project directory: `cd /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2`
   - Then re-run the command from the project root
   - Alternatively, use complete absolute paths to files

### Examples
```bash
# Preferred approach - from project directory
cd /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2
source venv/bin/activate && python script.py

# Alternative - using absolute paths
source /mnt/c/Users/<USER>/Documents/Programming/Crypto_App_V2/venv/bin/activate && python /path/to/script.py
```

### Why This Matters
- Virtual environment activation relies on relative paths
- MCP server configurations use project-relative paths
- Many scripts expect to run from the project root
- Prevents path resolution errors and missing file issues

### Timeout Handling Rules
**CRITICAL REQUIREMENT**: When a command or test is failing or erroring due to timeout, increase the timeout significantly to allow the process to finish.

Examples:
- Use `timeout 60s` instead of `timeout 30s` for complex operations
- For integration tests with database operations, use `timeout 120s` or more
- For ML training or data processing, use `timeout 300s` or remove timeout entirely
- When debugging hanging processes, add debug prints to identify bottlenecks

```bash
# Examples of appropriate timeout usage
source venv/bin/activate && timeout 60s python test_analytics_integration.py
source venv/bin/activate && timeout 120s python test_ensemble_standalone.py
source venv/bin/activate && timeout 300s python scripts/train_model.py
```

This prevents premature termination of legitimate long-running operations and ensures tests have adequate time to complete.

## MCP Server Configuration

The following MCP servers have been configured for the strategy ensemble system:

### Currently Installed
- **Supabase**: Portfolio analytics and real-time data storage
- **Telegram**: Real-time alerts and notifications  
- **Redis Trading**: Custom trading-specific caching (local)
- **Playwright**: Automated testing
- **GitHub**: Repository management
- **Jupyter**: Interactive development
- **Time**: Timestamp utilities

### Recently Added
- **Redis**: General real-time caching (`@modelcontextprotocol/server-redis`)
- **Weights & Biases**: ML experiment tracking (`wandb-mcp-server`)
- **MLflow**: Model deployment and lifecycle management
- **ZenML**: ML pipeline orchestration (local: `mcp-servers/mcp-zenml/`)
- **CoinCap**: Cryptocurrency data and cross-exchange validation (local: `mcp-servers/mcp-crypto-price/`)

### MCP Server Management Rules
**CRITICAL**: When fixing or dealing with MCPs, always refer to `claude mcp help` as the primary guide. There are no config files for MCPs - they are managed directly through Claude's MCP system.

### Testing MCP Servers
After installing any MCP server, test it using:
```bash
claude mcp list
```

For troubleshooting MCP issues:
```bash
claude mcp help
```

## Timestamp and Documentation Rules

**CRITICAL REQUIREMENT**: Always use the Time MCP to get the correct current timestamp before adding any dates to files or documentation.

### Timestamp Protocol
1. **Before Adding Timestamps**: Always call `mcp__Time-MCP__get_current_time` first
2. **Documentation Updates**: When updating task completion dates, milestones, or any time-sensitive information, use the accurate current time from the Time MCP
3. **Consistency**: Ensure all timestamps across files use the same format and accurate time

### Examples
```bash
# Before adding completion dates to documentation:
# 1. Get current time via MCP
# 2. Then update files with accurate timestamp

# ✅ Correct approach:
# Call mcp__Time-MCP__get_current_time first
# Then use returned timestamp: "2025-06-14T07:45:50.113Z" 
# Format as: "June 14, 2025" for human-readable dates

# ❌ Incorrect approach:
# Never guess or assume dates without checking Time MCP first
```

### Why This Matters
- Ensures accurate project timeline documentation
- Prevents confusion about when tasks were actually completed
- Maintains consistency across all project documentation
- Provides reliable audit trail for development progress

## Project Structure Notes

- Virtual environment located at `./venv/`
- MCP servers stored in `./mcp-servers/` for local implementations
- Requirements managed in `requirements.txt` and `requirements_current.txt`