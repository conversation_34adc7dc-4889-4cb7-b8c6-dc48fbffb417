# Portfolio Manager Design

**Component Owner**: AI
**Status**: Design Phase

## 1. Overview

The `Portfolio Manager` is the central component of the Strategy Ensemble architecture, replacing the legacy `Strategy Selector`. Its primary responsibility is to manage a dynamic portfolio of trading strategies, allocating capital and adjusting weights based on real-time market conditions, risk parameters, and performance metrics. This approach moves from selecting a single "best" strategy to running a diversified ensemble of strategies concurrently.

## 2. Core Functional Requirements

- **Multi-Strategy Execution**: Concurrently manage and execute multiple trading strategies.
- **Dynamic Weight Allocation**: Utilize the `ML Weight Optimizer` to dynamically assign capital allocation (weights) to each strategy in the portfolio.
- **Market Regime Adaptability**: Leverage the `Market Condition Detector` to adjust the strategy mix and weights in response to changing market regimes (e.g., Bullish, Bearish, Sideways).
- **Risk Integration**: Interface directly with the `Risk Management System` to enforce portfolio-level risk controls, including position sizing, drawdown limits, and volatility targeting.
- **State Management**: Maintain the real-time state of all active strategies, positions, and overall portfolio performance.
- **Extensibility**: Allow for the seamless addition or removal of new trading strategies without system interruption.

## 3. System Architecture and Dependencies

```mermaid
graph TD
    subgraph Core
        TE[Trading Engine]
    end

    subgraph Services
        MDS[Market Data Service]
        ES[Execution Service]
    end
    
    subgraph Portfolio Management
        PM[Portfolio Manager]
        RMS[Risk Management System]
        WO[ML Weight Optimizer]
    end

    subgraph Strategies
        TS[Trading Strategies]
        MCD[Market Condition Detector]
    end

    TE --> PM
    
    MDS --> MCD
    MDS --> TS

    MCD --> PM
    TS --> PM
    WO --> PM
    
    PM --> RMS
    RMS --> ES
```

- **Receives Input From**:
    - `Trading Engine`: High-level commands and system status.
    - `Market Condition Detector`: Real-time market regime analysis.
    - `Trading Strategies`: Signals and performance data from individual strategies.
    - `ML Weight Optimizer`: Optimized portfolio weights.
- **Sends Commands To**:
    - `Risk Management System`: Requests for position sizing and risk validation.
    - `Execution Service`: Final trade orders (via RMS).

## 4. Implementation Phases

1.  **Phase 1: Core Logic & Integration**
    - Implement the core `PortfolioManager` class structure.
    - Integrate with `Market Data Service` and `Trading Strategies`.
    - Basic implementation of concurrent strategy execution.

2.  **Phase 2: ML & Risk Integration**
    - Integrate with `ML Weight Optimizer` for dynamic allocation.
    - Integrate with the `Risk Management System` for position sizing and risk checks.
    - Implement the full logic for market regime adaptation.

3.  **Phase 3: Advanced Features & Optimization**
    - Implement cost-aware optimization (transaction fees, slippage).
    - Enhance state management and reporting for the dashboard.
    - Performance tuning and stress testing.

## 5. Testing Plan

- **Unit Tests**: Cover all individual methods within the `PortfolioManager` class.
- **Integration Tests**: Verify correct interaction with all dependent components (`ML Weight Optimizer`, `RMS`, `Strategies`, etc.).
- **End-to-End Tests**: Simulate full trading sessions to validate the entire ensemble workflow.
- **Backtesting**: Run the portfolio manager against historical data to evaluate performance against benchmarks.
