from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv
import os
import logging
import uvicorn
import asyncio
from datetime import datetime, timedelta

# Import API routers and configuration
from app.dashboard.api_router import router as api_router
from app.dashboard.ml_router import router as ml_router
from app.dashboard.api.websocket import router as websocket_router
from app.dashboard.api.analytics_websocket import router as analytics_websocket_router
from app.dashboard.api.analytics_websocket import startup_analytics, shutdown_analytics
from app.dashboard.config import settings

# Import ML components
from app.ml.training.trainer import ModelTrainer
from app.config.settings import settings as app_settings
from app.dashboard.api_router import get_exchange_client

# Load environment variables
load_dotenv()

# Configure logging: default to WARNING to suppress verbose INFO
logging.basicConfig(
    level=logging.WARNING,
    force=True,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log")
    ]
)

# Only INFO for dashboard and strategy selector; suppress ML logs
logging.getLogger("app.dashboard.main").setLevel(logging.INFO)
logging.getLogger("app.strategies.portfolio_manager").setLevel(logging.INFO)
logging.getLogger("app.ml").setLevel(logging.WARNING)
logging.getLogger("watchfiles").setLevel(logging.WARNING)
logging.getLogger("watchfiles.main").setLevel(logging.WARNING)
# Suppress uvicorn logs
logging.getLogger("uvicorn.error").setLevel(logging.WARNING)
logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
# Suppress asyncio and multipart verbosity
logging.getLogger("asyncio").setLevel(logging.WARNING)
logging.getLogger("python_multipart").setLevel(logging.WARNING)
# Suppress SQLAlchemy INFO logs
logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
logging.getLogger("sqlalchemy.engine.Engine").setLevel(logging.CRITICAL)

# Prevent SQLAlchemy engine logs from propagating
logging.getLogger("sqlalchemy.engine").propagate = False
logging.getLogger("sqlalchemy.engine.Engine").disabled = True

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Crypto Trading Bot Dashboard",
    description="API for the Multi-Strategy Crypto Auto Trader Dashboard",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins in development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(api_router)
app.include_router(ml_router, prefix=settings.API_PREFIX)
app.include_router(websocket_router)
app.include_router(analytics_websocket_router)

# Global variables for ML training
ml_training_task = None
last_training_time = None

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Welcome to the Crypto Trading Bot API"}

# ML training background task
async def ml_training_task_func():
    """Background task for ML model training."""
    global last_training_time

    # Create dependencies
    exchange_client = get_exchange_client()

    trainer = ModelTrainer(
        exchange_client=exchange_client,
        model_path=app_settings.ml_model_path
    )

    while True:
        try:
            # Check if ML weight optimization is enabled
            if not app_settings.ml_weight_optimization_enabled:
                await asyncio.sleep(60)  # Check again in 1 minute
                continue

            # Check if it's time to train
            current_time = datetime.now()
            if last_training_time is None or (current_time - last_training_time) >= timedelta(hours=app_settings.ml_training_interval_hours):
                logger.info(f"Starting scheduled ML model training (interval: {app_settings.ml_training_interval_hours} hours)")

                # Create training session
                session_info = await trainer.create_training_session(
                    symbol=app_settings.trading_symbol,
                    timeframe=app_settings.timeframe,
                    lookback_days=app_settings.ml_lookback_days,
                    optimize=app_settings.ml_hyperparameter_optimization,
                    n_trials=app_settings.ml_optimization_trials,
                    total_timesteps=app_settings.ml_training_timesteps
                )

                if session_info.get('status') == 'completed':
                    logger.info("Scheduled ML model training completed successfully")
                    last_training_time = current_time
                else:
                    logger.error(f"Scheduled ML model training failed: {session_info.get('error', 'Unknown error')}")

            # Sleep until next check (check every 10 minutes)
            await asyncio.sleep(600)

        except Exception as e:
            logger.error(f"Error in ML training task: {e}")
            await asyncio.sleep(600)  # Sleep and try again

# Startup event
@app.on_event("startup")
async def startup_event():
    """Startup event handler."""
    global ml_training_task

    # Configure log levels after Uvicorn startup
    logging.getLogger("app.services.exchange.binance_client").setLevel(logging.WARNING)
    logging.getLogger("app.services.account_service").setLevel(logging.WARNING)
    logging.getLogger("app.dashboard.api_router").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("python_multipart").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    # Disable SQLAlchemy engine logs
    logging.getLogger("sqlalchemy.engine").propagate = False
    logging.getLogger("sqlalchemy.engine.Engine").disabled = True
    logging.getLogger("app.strategies").setLevel(logging.WARNING)

    # Start ML training task
    if app_settings.ml_weight_optimization_enabled:
        logger.info("Starting ML training background task")
        ml_training_task = asyncio.create_task(ml_training_task_func())
    
    # Initialize real-time analytics service
    try:
        await startup_analytics()
        logger.info("✅ Real-time analytics service started")
    except Exception as e:
        logger.error(f"Failed to start analytics service: {e}")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler."""
    global ml_training_task

    # Cancel ML training task
    if ml_training_task:
        logger.info("Cancelling ML training background task")
        ml_training_task.cancel()
        try:
            await ml_training_task
        except asyncio.CancelledError:
            logger.info("ML training background task cancelled")
    
    # Shutdown analytics service
    try:
        await shutdown_analytics()
        logger.info("📊 Real-time analytics service stopped")
    except Exception as e:
        logger.error(f"Error stopping analytics service: {e}")

# Check if frontend mounting is disabled
if os.getenv('DISABLE_FRONTEND_MOUNT', '').lower() == 'true':
    logger.info("Frontend mounting is disabled by DISABLE_FRONTEND_MOUNT environment variable")
else:
    # Check if frontend build directory exists
    frontend_build_dir = os.path.join(os.path.dirname(__file__), "frontend", "build")
    if os.path.exists(frontend_build_dir):
        # Mount static files for frontend
        app.mount("/dashboard", StaticFiles(directory=frontend_build_dir, html=True), name="dashboard")
        logger.info(f"Mounted frontend build directory: {frontend_build_dir}")
    else:
        logger.warning(f"Frontend build directory not found: {frontend_build_dir}")
        logger.warning("To serve the frontend, run 'npm run build' in the frontend directory")

if __name__ == "__main__":
    # Set uvicorn log level based on our config level for consistency
    log_level = logging.getLevelName(logging.getLogger().getEffectiveLevel()).lower()
    uvicorn.run(
        "app.dashboard.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=log_level # Pass log level to uvicorn
    )
