#!/usr/bin/env python3
"""
Create Supabase Tables Programmatically
This script creates the missing tables using SQL via the Supabase REST API.
"""

import asyncio
import aiohttp
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('.env')

class SupabaseTableCreator:
    """Create Supabase tables programmatically"""
    
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        
        # Use the PostgREST endpoint for SQL execution
        self.rpc_url = f"{self.supabase_url}/rest/v1/rpc"
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json'
        }
    
    async def execute_sql(self, session: aiohttp.ClientSession, sql_command: str, description: str) -> bool:
        """Execute SQL command via Supabase RPC"""
        try:
            # Create a stored procedure to execute raw SQL
            create_function_sql = """
            CREATE OR REPLACE FUNCTION execute_sql(sql_text text)
            RETURNS text
            LANGUAGE plpgsql
            SECURITY DEFINER
            AS $$
            BEGIN
                EXECUTE sql_text;
                RETURN 'SUCCESS';
            EXCEPTION WHEN OTHERS THEN
                RETURN 'ERROR: ' || SQLERRM;
            END;
            $$;
            """
            
            # First, try to create the function (might already exist)
            try:
                async with session.post(
                    f"{self.supabase_url}/rest/v1/rpc/execute_sql",
                    headers=self.headers,
                    json={"sql_text": create_function_sql}
                ) as response:
                    pass  # Function creation might fail if it exists, that's OK
            except:
                pass
            
            # Now execute the actual SQL
            async with session.post(
                f"{self.supabase_url}/rest/v1/rpc/execute_sql",
                headers=self.headers,
                json={"sql_text": sql_command}
            ) as response:
                if response.status == 200:
                    result = await response.text()
                    if 'SUCCESS' in result:
                        print(f"✓ {description}")
                        return True
                    else:
                        print(f"✗ {description} - SQL Error: {result}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"✗ {description} - HTTP {response.status}: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"✗ {description} - Exception: {e}")
            return False
    
    async def create_tables(self):
        """Create all required tables"""
        print("Creating Supabase tables...")
        
        async with aiohttp.ClientSession() as session:
            
            # Table creation SQL commands
            table_sqls = [
                {
                    "sql": """
                    CREATE TABLE IF NOT EXISTS trade_executions (
                        id BIGSERIAL PRIMARY KEY,
                        strategy_name TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        action TEXT NOT NULL,
                        quantity DECIMAL(20, 8) NOT NULL,
                        price DECIMAL(20, 8) NOT NULL,
                        timestamp TIMESTAMPTZ NOT NULL,
                        metadata JSONB DEFAULT '{}',
                        created_at TIMESTAMPTZ DEFAULT NOW(),
                        updated_at TIMESTAMPTZ DEFAULT NOW()
                    );
                    """,
                    "description": "Creating trade_executions table"
                },
                {
                    "sql": """
                    CREATE TABLE IF NOT EXISTS market_data (
                        id BIGSERIAL PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        price DECIMAL(20, 8) NOT NULL,
                        volume DECIMAL(20, 8) NOT NULL,
                        high_24h DECIMAL(20, 8),
                        low_24h DECIMAL(20, 8),
                        change_24h DECIMAL(10, 6),
                        source TEXT NOT NULL,
                        timestamp TIMESTAMPTZ NOT NULL,
                        metadata JSONB DEFAULT '{}',
                        created_at TIMESTAMPTZ DEFAULT NOW()
                    );
                    """,
                    "description": "Creating market_data table"
                },
                {
                    "sql": """
                    CREATE TABLE IF NOT EXISTS cross_exchange_validations (
                        id BIGSERIAL PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        consensus_price DECIMAL(20, 8) NOT NULL,
                        price_variance DECIMAL(20, 8) DEFAULT 0,
                        data_quality_score DECIMAL(5, 4) DEFAULT 0,
                        source_count INTEGER DEFAULT 0,
                        outlier_sources TEXT[] DEFAULT '{}',
                        price_spread_pct DECIMAL(10, 6) DEFAULT 0,
                        volume_weighted_price DECIMAL(20, 8) DEFAULT 0,
                        timestamp TIMESTAMPTZ NOT NULL,
                        individual_sources JSONB DEFAULT '{}',
                        metadata JSONB DEFAULT '{}',
                        created_at TIMESTAMPTZ DEFAULT NOW()
                    );
                    """,
                    "description": "Creating cross_exchange_validations table"
                },
                {
                    "sql": """
                    CREATE TABLE IF NOT EXISTS ensemble_executions (
                        id BIGSERIAL PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        action TEXT NOT NULL,
                        quantity DECIMAL(20, 8) NOT NULL,
                        price DECIMAL(20, 8) NOT NULL,
                        confidence DECIMAL(5, 4) DEFAULT 0,
                        strategy_signals JSONB DEFAULT '{}',
                        execution_time_ms DECIMAL(10, 3) DEFAULT 0,
                        timestamp TIMESTAMPTZ NOT NULL,
                        metadata JSONB DEFAULT '{}',
                        created_at TIMESTAMPTZ DEFAULT NOW()
                    );
                    """,
                    "description": "Creating ensemble_executions table"
                }
            ]
            
            # Create tables
            success_count = 0
            for table_sql in table_sqls:
                success = await self.execute_sql(session, table_sql["sql"], table_sql["description"])
                if success:
                    success_count += 1
            
            # Create indexes
            index_sqls = [
                {
                    "sql": "CREATE INDEX IF NOT EXISTS idx_trade_executions_strategy ON trade_executions(strategy_name);",
                    "description": "Creating index on trade_executions.strategy_name"
                },
                {
                    "sql": "CREATE INDEX IF NOT EXISTS idx_trade_executions_symbol ON trade_executions(symbol);",
                    "description": "Creating index on trade_executions.symbol"
                },
                {
                    "sql": "CREATE INDEX IF NOT EXISTS idx_trade_executions_timestamp ON trade_executions(timestamp);",
                    "description": "Creating index on trade_executions.timestamp"
                },
                {
                    "sql": "CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol);",
                    "description": "Creating index on market_data.symbol"
                },
                {
                    "sql": "CREATE INDEX IF NOT EXISTS idx_market_data_source ON market_data(source);",
                    "description": "Creating index on market_data.source"
                },
                {
                    "sql": "CREATE INDEX IF NOT EXISTS idx_cross_exchange_symbol ON cross_exchange_validations(symbol);",
                    "description": "Creating index on cross_exchange_validations.symbol"
                },
                {
                    "sql": "CREATE INDEX IF NOT EXISTS idx_ensemble_executions_symbol ON ensemble_executions(symbol);",
                    "description": "Creating index on ensemble_executions.symbol"
                }
            ]
            
            print("\nCreating indexes...")
            for index_sql in index_sqls:
                await self.execute_sql(session, index_sql["sql"], index_sql["description"])
            
            return success_count == len(table_sqls)

async def main():
    """Main function"""
    print("=" * 60)
    print("CREATING SUPABASE TABLES")
    print("=" * 60)
    
    try:
        creator = SupabaseTableCreator()
        success = await creator.create_tables()
        
        if success:
            print("\n" + "=" * 60)
            print("✅ TABLE CREATION SUCCESSFUL")
            print("All required tables have been created!")
            print("=" * 60)
            
            # Now run the initialization check
            print("\nRunning initialization check...")
            from initialize_supabase import SupabaseInitializer
            initializer = SupabaseInitializer()
            init_success = await initializer.initialize_database()
            
            return init_success
        else:
            print("\n" + "=" * 60)
            print("❌ TABLE CREATION FAILED")
            print("Some tables could not be created.")
            print("=" * 60)
            return False
        
    except Exception as e:
        print(f"\n❌ Table creation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)