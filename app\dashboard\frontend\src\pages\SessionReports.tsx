import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON>,
  Button,
  Tab,
  Tabs,
  Box,
  Chip,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import DashboardLayout from '../components/DashboardLayout';
import {
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Speed as SpeedIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Visibility as VisibilityIcon,
  Compare as CompareIcon,
  FileDownload as FileDownloadIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { 
  Line<PERSON>hart, 
  Line, 
  AreaChart, 
  Area, 
  <PERSON>Axis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>ianGrid, 
  Tooltip as Recharts<PERSON>ooltip, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'recharts';
import { sessionReportsAPI } from '../services/api';
import SessionDetails from '../components/session/SessionDetails';
import { 
  websocketService, 
  WebSocketEventType, 
  LiveSessionMetricsEvent, 
  SessionRiskAlertEvent,
  SessionReportUpdateEvent,
  SystemStatusEvent 
} from '../services/websocket';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`session-tabpanel-${index}`}
      aria-labelledby={`session-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface SessionSummary {
  session_id: string;
  status: string;
  duration: {
    total_seconds: number;
    formatted: string;
    start_time: string;
    end_time?: string;
  };
  key_metrics: {
    total_pnl: number;
    total_return: number;
    win_rate: number;
    sharpe_ratio: number;
    max_drawdown: number;
    total_trades: number;
  };
  symbols_traded: string[];
  strategies_enabled: string[];
}

interface LiveSessionData {
  active: boolean;
  session_id?: string;
  live_metrics?: {
    current_pnl: number;
    unrealized_pnl: number;
    win_rate: number;
    active_trades: number;
    sharpe_ratio: number;
  };
  active_alerts?: Array<{
    level: string;
    message: string;
    timestamp: string;
  }>;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const SessionReports: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [sessions, setSessions] = useState<SessionSummary[]>([]);
  const [liveSession, setLiveSession] = useState<LiveSessionData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSessions, setSelectedSessions] = useState<string[]>([]);
  const [timeFilter, setTimeFilter] = useState('30');
  const [statusFilter, setStatusFilter] = useState('all');
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [wsConnected, setWsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Fetch sessions data
  const fetchSessions = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await sessionReportsAPI.getSessionsSummary(parseInt(timeFilter), statusFilter === 'all' ? undefined : statusFilter);
      setSessions(response.data.sessions || []);
    } catch (err: any) {
      setError(`Failed to fetch sessions: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Fetch live session data
  const fetchLiveSession = async () => {
    try {
      const response = await sessionReportsAPI.getLiveSessionReport();
      setLiveSession(response.data);
    } catch (err: any) {
      console.error('Failed to fetch live session:', err);
      setLiveSession({ active: false });
    }
  };

  useEffect(() => {
    fetchSessions();
    fetchLiveSession();
    
    // Connect to WebSocket for real-time updates
    websocketService.connectSessionReports();
    
    // Set up WebSocket event handlers
    const handleLiveSessionMetrics = (data: LiveSessionMetricsEvent) => {
      setLiveSession({
        active: true,
        session_id: data.session_id,
        live_metrics: {
          current_pnl: data.live_metrics.current_pnl,
          unrealized_pnl: data.live_metrics.unrealized_pnl,
          win_rate: data.live_metrics.win_rate,
          active_trades: data.live_metrics.active_trades,
          sharpe_ratio: data.live_metrics.sharpe_ratio,
        },
        active_alerts: data.active_alerts
      });
      setLastUpdate(new Date());
    };

    const handleSessionRiskAlert = (data: SessionRiskAlertEvent) => {
      // Add new alert to active alerts
      setLiveSession(prev => {
        if (!prev) return prev;
        
        const newAlert = {
          level: data.alert.level,
          message: data.alert.message,
          timestamp: data.timestamp
        };

        return {
          ...prev,
          active_alerts: [...(prev.active_alerts || []), newAlert]
        };
      });
    };

    const handleSessionReportUpdate = (data: SessionReportUpdateEvent) => {
      // Refresh sessions list when new session report is available
      fetchSessions();
    };

    const handleSystemStatus = (data: SystemStatusEvent) => {
      setWsConnected(data.status === 'online');
    };

    // Register WebSocket event handlers
    websocketService.on(WebSocketEventType.LIVE_SESSION_METRICS, handleLiveSessionMetrics);
    websocketService.on(WebSocketEventType.SESSION_RISK_ALERT, handleSessionRiskAlert);
    websocketService.on(WebSocketEventType.SESSION_REPORT_UPDATE, handleSessionReportUpdate);
    websocketService.on(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);
    
    // Set up refresh interval as fallback
    const interval = setInterval(() => {
      fetchLiveSession();
    }, 30000); // Refresh every 30 seconds as fallback

    return () => {
      clearInterval(interval);
      // Clean up WebSocket event handlers
      websocketService.off(WebSocketEventType.LIVE_SESSION_METRICS, handleLiveSessionMetrics);
      websocketService.off(WebSocketEventType.SESSION_RISK_ALERT, handleSessionRiskAlert);
      websocketService.off(WebSocketEventType.SESSION_REPORT_UPDATE, handleSessionReportUpdate);
      websocketService.off(WebSocketEventType.SYSTEM_STATUS, handleSystemStatus);
      websocketService.disconnect();
    };
  }, [timeFilter, statusFilter]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleViewSessionDetails = (sessionId: string) => {
    setSelectedSessionId(sessionId);
    setDetailsDialogOpen(true);
  };

  const handleCloseSessionDetails = () => {
    setDetailsDialogOpen(false);
    setSelectedSessionId(null);
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      completed: { color: 'success' as const, icon: <CheckCircleIcon /> },
      active: { color: 'primary' as const, icon: <SpeedIcon /> },
      failed: { color: 'error' as const, icon: <WarningIcon /> },
      paused: { color: 'warning' as const, icon: <WarningIcon /> }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default' as const, icon: null };
    
    return (
      <Chip
        label={status.toUpperCase()}
        color={config.color}
        size="small"
        icon={config.icon}
      />
    );
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // Live Session Overview Cards
  const LiveSessionOverview: React.FC = () => {
    const formatTimestamp = (date: Date | null) => {
      if (!date) return 'Never';
      return date.toLocaleTimeString();
    };

    if (!liveSession?.active) {
      return (
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Live Session Status
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip 
                      size="small" 
                      label={wsConnected ? 'Connected' : 'Disconnected'} 
                      color={wsConnected ? 'success' : 'error'} 
                      variant="outlined"
                    />
                    <Typography variant="caption" color="textSecondary">
                      Last Update: {formatTimestamp(lastUpdate)}
                    </Typography>
                  </Box>
                </Box>
                <Alert severity="info">No active trading session</Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      );
    }

    const metrics = liveSession.live_metrics!;
    
    return (
      <Grid container spacing={2}>
        {/* Connection Status Header */}
        <Grid item xs={12}>
          <Card>
            <CardContent sx={{ py: 1.5 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  Live Session Monitor
                  {liveSession.session_id && (
                    <Typography component="span" variant="caption" sx={{ ml: 1, fontFamily: 'monospace' }}>
                      ({liveSession.session_id.substring(0, 8)}...)
                    </Typography>
                  )}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip 
                    size="small" 
                    label={wsConnected ? 'Live' : 'Offline'} 
                    color={wsConnected ? 'success' : 'error'} 
                    variant="filled"
                    icon={wsConnected ? <SpeedIcon /> : <WarningIcon />}
                  />
                  <Typography variant="caption" color="textSecondary">
                    Last Update: {formatTimestamp(lastUpdate)}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Current P&L
              </Typography>
              <Typography variant="h4" color={metrics.current_pnl >= 0 ? 'success.main' : 'error.main'}>
                {formatCurrency(metrics.current_pnl)}
              </Typography>
              {metrics.current_pnl >= 0 ? <TrendingUpIcon color="success" /> : <TrendingDownIcon color="error" />}
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Win Rate
              </Typography>
              <Typography variant="h4">
                {formatPercentage(metrics.win_rate)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Trades
              </Typography>
              <Typography variant="h4">
                {metrics.active_trades}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Sharpe Ratio
              </Typography>
              <Typography variant="h4">
                {metrics.sharpe_ratio.toFixed(2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {liveSession.active_alerts && liveSession.active_alerts.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Active Alerts
                </Typography>
                {liveSession.active_alerts.map((alert, index) => (
                  <Alert 
                    key={index} 
                    severity={alert.level === 'critical' ? 'error' : alert.level === 'warning' ? 'warning' : 'info'}
                    sx={{ mb: 1 }}
                  >
                    {alert.message}
                  </Alert>
                ))}
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    );
  };

  // Sessions History Table
  const SessionsHistoryTable: React.FC = () => {
    return (
      <Card>
        <CardHeader 
          title="Session History"
          action={
            <Box sx={{ display: 'flex', gap: 2 }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Time Range</InputLabel>
                <Select
                  value={timeFilter}
                  label="Time Range"
                  onChange={(e) => setTimeFilter(e.target.value)}
                >
                  <MenuItem value="7">Last 7 days</MenuItem>
                  <MenuItem value="30">Last 30 days</MenuItem>
                  <MenuItem value="90">Last 90 days</MenuItem>
                </Select>
              </FormControl>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                </Select>
              </FormControl>
              <IconButton onClick={fetchSessions} size="small">
                <RefreshIcon />
              </IconButton>
            </Box>
          }
        />
        <CardContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">{error}</Alert>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Session ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Duration</TableCell>
                    <TableCell align="right">Total P&L</TableCell>
                    <TableCell align="right">Return</TableCell>
                    <TableCell align="right">Win Rate</TableCell>
                    <TableCell align="right">Trades</TableCell>
                    <TableCell>Strategies</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sessions.map((session) => (
                    <TableRow key={session.session_id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {session.session_id.substring(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {getStatusChip(session.status)}
                      </TableCell>
                      <TableCell>{session.duration.formatted}</TableCell>
                      <TableCell align="right">
                        <Typography color={session.key_metrics.total_pnl >= 0 ? 'success.main' : 'error.main'}>
                          {formatCurrency(session.key_metrics.total_pnl)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography color={session.key_metrics.total_return >= 0 ? 'success.main' : 'error.main'}>
                          {formatPercentage(session.key_metrics.total_return)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">{formatPercentage(session.key_metrics.win_rate)}</TableCell>
                      <TableCell align="right">{session.key_metrics.total_trades}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                          {session.strategies_enabled.map((strategy, index) => (
                            <Chip key={index} label={strategy} size="small" variant="outlined" />
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small" onClick={() => handleViewSessionDetails(session.session_id)}>
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Add to Comparison">
                          <IconButton size="small" onClick={() => {
                            if (selectedSessions.includes(session.session_id)) {
                              setSelectedSessions(prev => prev.filter(id => id !== session.session_id));
                            } else {
                              setSelectedSessions(prev => [...prev, session.session_id]);
                            }
                          }}>
                            <CompareIcon color={selectedSessions.includes(session.session_id) ? 'primary' : 'inherit'} />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    );
  };

  // Session Comparison Component
  const SessionComparison: React.FC = () => {
    const [comparisonData, setComparisonData] = useState<any[]>([]);
    const [comparisonLoading, setComparisonLoading] = useState(false);
    const [comparisonError, setComparisonError] = useState<string | null>(null);

    // Fetch comparison data for selected sessions
    const fetchComparisonData = async () => {
      if (selectedSessions.length === 0) return;
      
      setComparisonLoading(true);
      setComparisonError(null);
      
      try {
        const sessionPromises = selectedSessions.map(async (sessionId) => {
          const [reportResponse, analyticsResponse] = await Promise.all([
            sessionReportsAPI.getSessionReport(sessionId),
            sessionReportsAPI.getSessionAnalytics(sessionId)
          ]);
          
          return {
            session_id: sessionId,
            report: reportResponse.data,
            analytics: analyticsResponse.data
          };
        });
        
        const results = await Promise.all(sessionPromises);
        setComparisonData(results);
      } catch (err: any) {
        setComparisonError(`Failed to fetch comparison data: ${err.message}`);
      } finally {
        setComparisonLoading(false);
      }
    };

    useEffect(() => {
      fetchComparisonData();
    }, [selectedSessions]);

    const removeSessionFromComparison = (sessionId: string) => {
      setSelectedSessions(prev => prev.filter(id => id !== sessionId));
    };

    // Generate mock comparison charts data
    const generateComparisonChartData = () => {
      if (comparisonData.length === 0) return [];
      
      // Generate mock P&L over time data
      const timePoints = 24; // 24 hours
      const chartData = [];
      
      for (let i = 0; i < timePoints; i++) {
        const dataPoint: any = {
          hour: i,
          timestamp: new Date(Date.now() - (timePoints - i) * 60 * 60 * 1000).toISOString()
        };
        
        comparisonData.forEach((session, index) => {
          // Mock P&L progression based on final P&L
          const finalPnl = session.report?.key_metrics?.total_pnl || 0;
          const progress = (i + 1) / timePoints;
          const volatility = Math.sin(i * 0.5) * 0.1; // Add some volatility
          dataPoint[`session_${index}`] = finalPnl * progress + (finalPnl * volatility);
        });
        
        chartData.push(dataPoint);
      }
      
      return chartData;
    };

    const chartData = generateComparisonChartData();

    // Performance ranking
    const getPerformanceRanking = () => {
      return comparisonData
        .map((session, index) => ({
          ...session,
          index,
          pnl: session.report?.key_metrics?.total_pnl || 0,
          return_pct: session.report?.key_metrics?.total_return || 0,
          win_rate: session.report?.key_metrics?.win_rate || 0,
          sharpe: session.report?.key_metrics?.sharpe_ratio || 0
        }))
        .sort((a, b) => b.pnl - a.pnl);
    };

    const rankedSessions = getPerformanceRanking();

    if (selectedSessions.length === 0) {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 6 }}>
                <CompareIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  No Sessions Selected for Comparison
                </Typography>
                <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
                  Select multiple sessions from the Session History tab to compare their performance,
                  strategies, and metrics side by side.
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => setTabValue(1)}
                  startIcon={<CompareIcon />}
                >
                  Go to Session History
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      );
    }

    return (
      <Grid container spacing={3}>
        {/* Selected Sessions Header */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title={`Session Comparison (${selectedSessions.length} sessions)`}
              action={
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={fetchComparisonData}
                  disabled={comparisonLoading}
                >
                  Refresh
                </Button>
              }
            />
            <CardContent>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {selectedSessions.map((sessionId, index) => (
                  <Chip
                    key={sessionId}
                    label={`Session ${sessionId.substring(0, 8)}...`}
                    onDelete={() => removeSessionFromComparison(sessionId)}
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {comparisonLoading ? (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          </Grid>
        ) : comparisonError ? (
          <Grid item xs={12}>
            <Alert severity="error">{comparisonError}</Alert>
          </Grid>
        ) : (
          <>
            {/* Performance Summary Cards */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Performance Summary" />
                <CardContent>
                  <Grid container spacing={2}>
                    {rankedSessions.map((session, index) => (
                      <Grid item xs={12} sm={6} md={4} key={session.session_id}>
                        <Card variant="outlined" sx={{ 
                          border: index === 0 ? 2 : 1, 
                          borderColor: index === 0 ? 'success.main' : 'divider' 
                        }}>
                          <CardContent>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                              <Typography variant="h6" component="div">
                                Session {session.session_id.substring(0, 8)}...
                              </Typography>
                              {index === 0 && (
                                <Chip label="Best" color="success" size="small" />
                              )}
                            </Box>
                            <Typography variant="h4" color={session.pnl >= 0 ? 'success.main' : 'error.main'} gutterBottom>
                              {formatCurrency(session.pnl)}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Return: {formatPercentage(session.return_pct)}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Win Rate: {formatPercentage(session.win_rate)}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Sharpe: {session.sharpe.toFixed(2)}
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Comparison Table */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Detailed Metrics Comparison" />
                <CardContent>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Metric</TableCell>
                          {comparisonData.map((session, index) => (
                            <TableCell key={session.session_id} align="right">
                              Session {session.session_id.substring(0, 8)}...
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell><strong>Total P&L</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              <Typography color={session.report?.key_metrics?.total_pnl >= 0 ? 'success.main' : 'error.main'}>
                                {formatCurrency(session.report?.key_metrics?.total_pnl || 0)}
                              </Typography>
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Total Return</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              <Typography color={session.report?.key_metrics?.total_return >= 0 ? 'success.main' : 'error.main'}>
                                {formatPercentage(session.report?.key_metrics?.total_return || 0)}
                              </Typography>
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Win Rate</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              {formatPercentage(session.report?.key_metrics?.win_rate || 0)}
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Sharpe Ratio</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              {(session.report?.key_metrics?.sharpe_ratio || 0).toFixed(2)}
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Max Drawdown</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              <Typography color="error.main">
                                {formatPercentage(session.report?.key_metrics?.max_drawdown || 0)}
                              </Typography>
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Total Trades</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              {session.report?.key_metrics?.total_trades || 0}
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Duration</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              {session.report?.duration?.formatted || 'N/A'}
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* P&L Comparison Chart */}
            <Grid item xs={12} lg={8}>
              <Card>
                <CardHeader title="P&L Over Time Comparison" />
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="hour" 
                        label={{ value: 'Hours', position: 'insideBottom', offset: -5 }} 
                      />
                      <YAxis 
                        label={{ value: 'P&L ($)', angle: -90, position: 'insideLeft' }}
                        tickFormatter={(value) => `$${value.toFixed(0)}`}
                      />
                      <RechartsTooltip 
                        formatter={(value: any, name: any) => [formatCurrency(value), `Session ${name.split('_')[1]}`]}
                        labelFormatter={(hour) => `Hour ${hour}`}
                      />
                      {comparisonData.map((session, index) => (
                        <Line
                          key={session.session_id}
                          type="monotone"
                          dataKey={`session_${index}`}
                          stroke={COLORS[index % COLORS.length]}
                          strokeWidth={2}
                          dot={false}
                          name={`session_${index}`}
                        />
                      ))}
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Performance Metrics Chart */}
            <Grid item xs={12} lg={4}>
              <Card>
                <CardHeader title="Performance Metrics" />
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <PieChart>
                      <Pie
                        data={rankedSessions.map((session, index) => ({
                          name: `Session ${session.session_id.substring(0, 8)}...`,
                          value: Math.max(session.pnl, 0), // Only show positive P&L
                          fill: COLORS[index % COLORS.length]
                        }))}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ value }) => formatCurrency(value)}
                      >
                        {rankedSessions.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <RechartsTooltip formatter={(value) => formatCurrency(value as number)} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Strategy Comparison */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Strategy Usage Comparison" />
                <CardContent>
                  <Grid container spacing={2}>
                    {comparisonData.map((session, index) => (
                      <Grid item xs={12} md={6} lg={4} key={session.session_id}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="h6" gutterBottom>
                              Session {session.session_id.substring(0, 8)}...
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                              {session.report?.strategies_enabled?.map((strategy: string, stratIndex: number) => (
                                <Chip
                                  key={stratIndex}
                                  label={strategy}
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                />
                              )) || <Typography variant="body2" color="textSecondary">No strategies data</Typography>}
                            </Box>
                            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                              Symbols: {session.report?.symbols_traded?.join(', ') || 'N/A'}
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Risk Metrics Comparison */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Risk Metrics Analysis" />
                <CardContent>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Risk Metric</TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              Session {session.session_id.substring(0, 8)}...
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell><strong>Max Drawdown</strong></TableCell>
                          {comparisonData.map((session) => (
                            <TableCell key={session.session_id} align="right">
                              <Typography color="error.main">
                                {formatPercentage(session.report?.key_metrics?.max_drawdown || 0)}
                              </Typography>
                            </TableCell>
                          ))}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Volatility (est.)</strong></TableCell>
                          {comparisonData.map((session) => {
                            // Mock volatility calculation based on P&L and return
                            const volatility = Math.abs(session.report?.key_metrics?.total_return || 0) * 2;
                            return (
                              <TableCell key={session.session_id} align="right">
                                {formatPercentage(volatility)}
                              </TableCell>
                            );
                          })}
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Risk Score</strong></TableCell>
                          {comparisonData.map((session) => {
                            // Mock risk score calculation
                            const drawdown = Math.abs(session.report?.key_metrics?.max_drawdown || 0);
                            const winRate = session.report?.key_metrics?.win_rate || 0;
                            const riskScore = Math.max(0, Math.min(10, (drawdown * 10) - (winRate * 5)));
                            const riskLevel = riskScore < 3 ? 'Low' : riskScore < 7 ? 'Medium' : 'High';
                            const riskColor = riskScore < 3 ? 'success.main' : riskScore < 7 ? 'warning.main' : 'error.main';
                            
                            return (
                              <TableCell key={session.session_id} align="right">
                                <Typography color={riskColor}>
                                  {riskLevel} ({riskScore.toFixed(1)})
                                </Typography>
                              </TableCell>
                            );
                          })}
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Recommendations */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Performance Insights & Recommendations" />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Alert severity="success" sx={{ mb: 2 }}>
                        <Typography variant="h6" gutterBottom>Best Performing Session</Typography>
                        <Typography variant="body2">
                          Session {rankedSessions[0]?.session_id.substring(0, 8)}... achieved the highest P&L of{' '}
                          {formatCurrency(rankedSessions[0]?.pnl || 0)} with a win rate of{' '}
                          {formatPercentage(rankedSessions[0]?.win_rate || 0)}.
                        </Typography>
                      </Alert>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="h6" gutterBottom>Strategy Insights</Typography>
                        <Typography variant="body2">
                          {comparisonData.length > 1 ? (
                            `Comparing ${comparisonData.length} sessions shows varying strategy effectiveness. 
                            Consider analyzing the most successful strategy combinations for future sessions.`
                          ) : (
                            'Add more sessions to comparison for deeper strategic insights.'
                          )}
                        </Typography>
                      </Alert>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Alert severity="warning" sx={{ mb: 2 }}>
                        <Typography variant="h6" gutterBottom>Risk Analysis</Typography>
                        <Typography variant="body2">
                          Monitor maximum drawdown levels across sessions. Sessions with drawdown exceeding 10% 
                          should be reviewed for risk management improvements.
                        </Typography>
                      </Alert>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Alert severity="info">
                        <Typography variant="h6" gutterBottom>Optimization Opportunities</Typography>
                        <Typography variant="body2">
                          Sessions with high win rates but low total returns may benefit from position sizing 
                          adjustments. Consider implementing dynamic position sizing based on market conditions.
                        </Typography>
                      </Alert>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </>
        )}
      </Grid>
    );
  };


  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom>
        Session Reports & Analytics
      </Typography>
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="session reports tabs">
            <Tab label="Live Monitoring" />
            <Tab label="Session History" />
            <Tab label="Analytics" />
            <Tab label="Comparison" />
          </Tabs>
        </Box>

      <TabPanel value={tabValue} index={0}>
        <LiveSessionOverview />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <SessionsHistoryTable />
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Performance Analytics
                </Typography>
                <Typography color="textSecondary">
                  Advanced analytics coming soon...
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <SessionComparison />
      </TabPanel>

      {/* Session Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={handleCloseSessionDetails}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          Session Details
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {selectedSessionId && (
            <SessionDetails
              sessionId={selectedSessionId}
              onClose={handleCloseSessionDetails}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseSessionDetails}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SessionReports;