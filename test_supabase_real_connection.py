#!/usr/bin/env python3
"""
Test Supabase Real Connection
Tests the actual Supabase service with real credentials from .env
"""

import asyncio
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path  
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

async def test_real_supabase_connection():
    """Test real Supabase connection with credentials from .env"""
    print("🔍 Testing Real Supabase Connection...")
    print(f"🌐 Supabase URL: {os.getenv('SUPABASE_URL')}")
    print(f"🔑 Supabase Key: {os.getenv('SUPABASE_KEY')[:20]}...")
    
    try:
        from app.services.mcp.supabase_service import SupabaseService
        
        # Initialize with real credentials from .env
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        if not url or not key:
            print("❌ Supabase credentials not found in .env file")
            return False
            
        service = SupabaseService(url=url, key=key)
        
        # Test connection
        connection_ok = await service.test_connection()
        if connection_ok:
            print("✅ Real Supabase connection successful!")
        else:
            print("⚠️ Supabase connection failed, using mock")
            return False
        
        # Test storing real portfolio metrics
        real_metrics = {
            "total_pnl": 2450.75,
            "sharpe_ratio": 1.8,
            "max_drawdown": -0.05,
            "win_rate": 0.72,
            "active_positions": 2,
            "portfolio_value": 102450.75,
            "strategy_contributions": {
                "GridStrategy": 1200.30, 
                "TechnicalAnalysisStrategy": 1250.45
            },
            "correlation_matrix": {
                "GridStrategy": {"TechnicalAnalysisStrategy": 0.25}
            },
            "test_run": True,
            "timestamp": datetime.now().isoformat()
        }
        
        print("📊 Storing real portfolio metrics...")
        record_id = await service.store_portfolio_metrics(real_metrics)
        if record_id:
            print(f"✅ Real portfolio metrics stored with ID: {record_id}")
        else:
            print("⚠️ Failed to store real metrics")
            return False
            
        # Test storing trade execution data
        trade_data = {
            "strategy_name": "GridStrategy",
            "symbol": "BTCUSDT", 
            "action": "BUY",
            "quantity": 0.05,
            "price": 43250.50,
            "timestamp": datetime.now().isoformat(),
            "confidence": 0.85,
            "weight": 0.4,
            "position_size": 0.08,
            "market_conditions": {
                "volatility": 0.025,
                "volume": 125000000,
                "trend": "bullish"
            }
        }
        
        print("📈 Storing real trade execution...")
        await service.store_trade_execution(trade_data)
        print("✅ Real trade execution stored")
        
        # Test storing strategy weights
        weights = {
            "GridStrategy": 0.4,
            "TechnicalAnalysisStrategy": 0.35, 
            "TrendFollowingStrategy": 0.25,
            "confidence": 0.78
        }
        
        print("⚖️ Storing strategy weights...")
        await service.store_strategy_weights(weights)
        print("✅ Strategy weights stored")
        
        # Test retrieving recent trades
        print("📋 Retrieving recent trades...")
        recent_trades = await service.get_recent_trades(limit=5)
        print(f"✅ Retrieved {len(recent_trades)} recent trades")
        
        if recent_trades:
            latest_trade = recent_trades[0]
            print(f"📊 Latest trade: {latest_trade.get('strategy_name')} - {latest_trade.get('action')} - {latest_trade.get('symbol')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Real Supabase test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_supabase_analytics_integration():
    """Test real-time analytics with real Supabase"""
    print("\n📊 Testing Real-time Analytics with Real Supabase...")
    
    try:
        from app.services.mcp.supabase_service import SupabaseService
        from app.services.mcp.supabase_realtime_analytics import SupabaseRealTimeAnalytics
        
        # Initialize with real credentials
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        supabase_service = SupabaseService(url=url, key=key)
        analytics = SupabaseRealTimeAnalytics(supabase_service)
        
        print("✅ Real-time analytics service initialized with real Supabase")
        
        # Test alert configuration
        print(f"⚠️ Alert rules configured: {len(analytics.alert_rules)}")
        
        # Test subscription mechanism  
        def test_callback(data):
            print(f"📢 Real-time callback received: {type(data)}")
        
        analytics.subscribe_to_updates("portfolio_update", "test_subscriber", test_callback)
        print("✅ Real-time subscription mechanism working")
        
        # Test dashboard summary (with real data if available)
        try:
            summary = await asyncio.wait_for(analytics.get_dashboard_summary(), timeout=10.0)
            print(f"📈 Dashboard summary: {summary['status']}")
            if summary['status'] != 'no_data':
                print(f"💰 Portfolio Value: ${summary.get('portfolio_value', 0):,.2f}")
                print(f"📊 Total PnL: ${summary.get('total_pnl', 0):,.2f}")
        except asyncio.TimeoutError:
            print("📈 Dashboard summary: timeout (using fallback)")
        except Exception as e:
            print(f"📈 Dashboard summary: {e}")
        
        print("✅ Real-time analytics integration working")
        return True
        
    except Exception as e:
        print(f"❌ Real-time analytics test failed: {e}")
        return False

async def main():
    """Run all real Supabase tests."""
    print("🚀 Starting Real Supabase Integration Tests")
    print("=" * 60)
    
    # Check environment variables first
    if not os.getenv('SUPABASE_URL') or not os.getenv('SUPABASE_KEY'):
        print("❌ Supabase credentials not found in environment")
        print("💡 Make sure .env file contains SUPABASE_URL and SUPABASE_KEY")
        return
    
    tests = [
        ("Real Supabase Connection", test_real_supabase_connection),
        ("Real-time Analytics Integration", test_supabase_analytics_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📋 REAL SUPABASE TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(tests)} tests, {passed} passed, {failed} failed")
    
    if failed > 0:
        print(f"\n⚠️ {failed} test(s) failed. Check the output above for details.")
        print("💡 Verify Supabase project is active and credentials are correct")
    else:
        print("\n🎉 All real Supabase tests passed!")
        print("🌟 Real-time analytics integration is ready for production!")

if __name__ == "__main__":
    asyncio.run(main())