import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Tooltip,
  Box,
  Typography,
  LinearProgress
} from '@mui/material';
import { Trade } from '../../types/trade';
import { getStatusColor, formatPrice, formatDateTime } from '../../utils/formatters';

interface TradesTableProps {
  trades: Trade[];
  title: string;
}

const TradesTable: React.FC<TradesTableProps> = ({ trades, title }) => {
  const calculateProgress = (trade: Trade) => {
    if (!trade.entry_fill_price || !trade.current_price) return 0;
    if (trade.status.includes('CLOSED')) return 100;

    const entry = trade.entry_fill_price;
    const current = trade.current_price;

    if (trade.entry_side === 'BUY') {
      if (trade.tp_price && current >= trade.tp_price) return 100;
      if (trade.sl_price && current <= trade.sl_price) return 0;
      if (trade.tp_price && trade.sl_price) {
        return ((current - trade.sl_price) / (trade.tp_price - trade.sl_price)) * 100;
      }
    } else { // SHORT
      if (trade.tp_price && current <= trade.tp_price) return 100;
      if (trade.sl_price && current >= trade.sl_price) return 0;
      if (trade.tp_price && trade.sl_price) {
        return ((trade.sl_price - current) / (trade.sl_price - trade.tp_price)) * 100;
      }
    }
    return 50; // Default progress if not determinable
  };

  return (
    <TableContainer component={Paper} sx={{ mb: 4 }}>
      <Typography variant="h6" sx={{ p: 2 }}>{title}</Typography>
      <Table stickyHeader>
        <TableHead>
          <TableRow>
            <TableCell>Symbol</TableCell>
            <TableCell>Side</TableCell>
            <TableCell>Entry Price</TableCell>
            <TableCell>SL Price</TableCell>
            <TableCell>TP Price</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>PnL</TableCell>
            <TableCell>Last Updated</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {trades.map((trade) => (
            <TableRow key={trade.trade_id}>
              <TableCell>{trade.symbol}</TableCell>
              <TableCell>
                <Chip
                  label={trade.entry_side}
                  color={trade.entry_side === 'BUY' ? 'success' : 'error'}
                  size="small"
                />
              </TableCell>
              <TableCell>{formatPrice(trade.entry_fill_price)}</TableCell>
              <TableCell>{formatPrice(trade.sl_price)}</TableCell>
              <TableCell>{formatPrice(trade.tp_price)}</TableCell>
              <TableCell>
                 <Tooltip title={trade.status} arrow>
                    <Chip
                        label={trade.status}
                        color={getStatusColor(trade.status)}
                        size="small"
                    />
                </Tooltip>
              </TableCell>
              <TableCell>
                <Box sx={{ width: '100px' }}>
                  <LinearProgress
                    variant="determinate"
                    value={calculateProgress(trade)}
                    color={trade.pnl && trade.pnl >= 0 ? 'success' : 'error'}
                  />
                  <Typography variant="body2" color="text.secondary">
                    {trade.pnl ? `${trade.pnl.toFixed(2)}` : 'N/A'}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>{formatDateTime(trade.updated_at)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TradesTable; 