// User types
export interface User {
  username: string;
  email?: string;
  is_active: boolean;
}

// Authentication types
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
}

// Trading types
export interface AccountInfo {
  asset: string;
  free: string;
  locked: string;
  total_equity: string;
  unrealized_pnl: string;
  margin_balance: string;
  timestamp: string;
}

export interface MarketData {
  timestamp: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface Position {
  symbol: string;
  entryPrice: number;
  markPrice: number;
  positionAmt: number;
  unrealizedProfit: number;
  leverage: number;
  marginType: string;
  isolatedMargin: number;
  positionSide: string;
  updateTime: number;
}

export interface TradeHistory {
  id: number;
  symbol: string;
  side: string;
  price: number;
  quantity: number;
  pnl: number;
  timestamp: string;
}

export interface Order {
  orderId: string;
  symbol: string;
  status: string;
  clientOrderId: string;
  price: string;
  origQty: string;
  executedQty: string;
  type: string;
  side: string;
  stopPrice?: string;
  time: number;
  updateTime: number;
  isWorking: boolean;
}

export interface StrategyConfig {
  active_strategy: string;
  risk_percentage: number;
  stop_loss_percentage: number;
  take_profit_percentage: number;
}

export interface AccountSettings {
  use_trailing_stop: boolean;
  trailing_stop_percentage: number;
  max_leverage: number;
  enable_notifications: boolean;
}

export interface StrategyPerformance {
  strategy: string;
  total_return: number;
  max_drawdown: number;
  sharpe_ratio: number;
  win_rate: number;
  profit_factor: number;
}

export interface ActiveStrategy {
  active_strategy: string;
  market_conditions: {
    trend_strength: number;
    volatility: string;
    market_structure: string;
    price_change_24h?: number;
    volume_ratio?: number;
    price_range_pct?: number;
  };
  selected_at: string;
}

export interface TradingConfig {
  trading: {
    symbol: string;
    risk_per_trade: number;
    max_position: number;
    use_trailing_stop: boolean;
  };
  strategies: {
    grid: {
      enabled: boolean;
      grid_levels: number;
      grid_range_percent: number;
    };
    technical: {
      enabled: boolean;
      indicators: string[];
    };
    trend: {
      enabled: boolean;
      adx_threshold: number;
      trailing_stop_atr: number;
    };
  };
}

// Notification types
export interface Notification {
  id?: number;
  type: string; // 'info', 'warning', 'error', 'success'
  message: string;
  created_at?: string;
  read?: boolean;
}

// Strategy comparison types
export interface StrategyMetrics {
  total_return: number;
  max_drawdown: number;
  sharpe_ratio: number;
  win_rate: number;
  profit_factor: number;
  daily_returns: number[];
}

export interface StrategyComparison {
  performance_metrics: {
    grid_trading: StrategyMetrics;
    technical_analysis: StrategyMetrics;
    trend_following: StrategyMetrics;
  };
  time_series: StrategyTimeSeriesPoint[];
}

export interface StrategyTimeSeriesPoint {
  date: string;
  grid: number;
  technical: number;
  trend: number;
}

// Risk management types
export interface PortfolioAllocation {
  name: string;
  value: number;
  color: string;
}

export interface StopLossData {
  name: string;
  value: number;
}

export interface ExposureMetrics {
  current_exposure: number;
  max_allowed_exposure: number;
  current_leverage: number;
  max_leverage: number;
  current_risk_per_trade: number;
  max_risk_per_trade: number;
  daily_drawdown: number;
  max_daily_drawdown: number;
}

export interface RiskMetrics {
  portfolio_allocation: PortfolioAllocation[];
  stop_loss_data: StopLossData[];
  exposure_metrics: ExposureMetrics;
}

export interface TradeSummary {
  total_trades: number;
  total_wins: number;
  total_losses: number;
  win_rate: number;
  total_profit: number;
  total_loss: number;
  profit_factor: number;
  unrealized_pnl: number;
}

// Market ticker (for /api/market/ticker)
export interface MarketTicker {
  symbol: string;
  price: string;
  timestamp: string;
}

// Managed trade (for /api/trading/active-trades and /api/trading/recent-trades)
export interface ManagedTrade {
  trade_id: string;
  symbol: string;
  entry_side: string;
  entry_price: number;
  entry_qty: number;
  sl_price: number;
  tp_price: number;
  exit_price: number;
  status: string;
  created_at: string;
  closed_at: string | null;
  pnl: number;
  pnl_percentage: number;
  message?: string;
}

// ML API responses
export interface MLTrainingResponse {
  success: boolean;
  message: string;
  training_id?: string;
  start_time?: string;
  end_time?: string;
  metrics?: Record<string, any>;
  error?: string;
}

export interface MLWeightsResponse {
  success: boolean;
  weights?: Record<string, Record<string, number>>;
  strategy_weights?: Record<string, number>;
  timestamp: string;
  model_info?: Record<string, any>;
  error?: string;
}

export interface MLBacktestResponse {
  success: boolean;
  comparison?: Record<string, any>;
  fixed_metrics?: Record<string, number>;
  ml_metrics?: Record<string, number>;
  error?: string;
}

export interface MLModelInfoResponse {
  success: boolean;
  model_info?: Record<string, any>;
  error?: string;
}

export interface MLStatusResponse {
  enabled: boolean;
  model_path: string;
  training_interval_hours: number;
  lookback_days: number;
  window_size: number;
  training_timesteps: number;
  hyperparameter_optimization: boolean;
  optimization_trials: number;
  reward_function: string;
  trading_symbol: string;
  timeframe: string;
}

export interface MLToggleResponse {
  success: boolean;
  enabled?: boolean;
  error?: string;
} 