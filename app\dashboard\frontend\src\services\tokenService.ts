/**
 * Token Service
 * 
 * This service handles token management for authentication.
 */

// Token storage keys
const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const TOKEN_TYPE_KEY = 'token_type';

/**
 * Save tokens to local storage
 * 
 * @param accessToken Access token
 * @param refreshToken Refresh token
 * @param tokenType Token type (e.g., 'bearer')
 */
export const saveTokens = (accessToken: string, refreshToken: string, tokenType: string): void => {
  localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
  localStorage.setItem(TOKEN_TYPE_KEY, tokenType);
};

/**
 * Get access token from local storage
 * 
 * @returns Access token or null if not found
 */
export const getAccessToken = (): string | null => {
  return localStorage.getItem(ACCESS_TOKEN_KEY);
};

/**
 * Get refresh token from local storage
 * 
 * @returns Refresh token or null if not found
 */
export const getRefreshToken = (): string | null => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Get token type from local storage
 * 
 * @returns Token type or null if not found
 */
export const getTokenType = (): string | null => {
  return localStorage.getItem(TOKEN_TYPE_KEY);
};

/**
 * Clear all tokens from local storage
 */
export const clearTokens = (): void => {
  localStorage.removeItem(ACCESS_TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(TOKEN_TYPE_KEY);
};

/**
 * Check if the user is authenticated
 * 
 * @returns True if the user is authenticated, false otherwise
 */
export const isAuthenticated = (): boolean => {
  return !!getAccessToken();
};

/**
 * Parse JWT token
 * 
 * @param token JWT token
 * @returns Parsed token payload or null if invalid
 */
export const parseJwt = (token: string): any => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error parsing JWT token:', error);
    return null;
  }
};

/**
 * Check if the access token is expired
 * 
 * @returns True if the token is expired, false otherwise
 */
export const isTokenExpired = (): boolean => {
  const token = getAccessToken();
  if (!token) return true;

  try {
    const payload = parseJwt(token);
    if (!payload || !payload.exp) return true;

    // exp is in seconds, Date.now() is in milliseconds
    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();

    // Return true if the token is expired or will expire in the next 60 seconds
    return expirationTime <= currentTime + 60000;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

/**
 * Check if the refresh token is expired
 * 
 * @returns True if the token is expired, false otherwise
 */
export const isRefreshTokenExpired = (): boolean => {
  const token = getRefreshToken();
  if (!token) return true;

  try {
    const payload = parseJwt(token);
    if (!payload || !payload.exp) return true;

    // exp is in seconds, Date.now() is in milliseconds
    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();

    return expirationTime <= currentTime;
  } catch (error) {
    console.error('Error checking refresh token expiration:', error);
    return true;
  }
};

export default {
  saveTokens,
  getAccessToken,
  getRefreshToken,
  getTokenType,
  clearTokens,
  isAuthenticated,
  parseJwt,
  isTokenExpired,
  isRefreshTokenExpired,
};
