#!/usr/bin/env python3
"""
Standalone Test Suite for Task 3.2.1: Paper Trading Environment
Tests the complete paper trading deployment with performance validation.

Test Coverage:
- Paper trading portfolio operations
- Simulated order execution
- Cost optimization integration
- W&B performance tracking
- System reliability and performance
"""

import asyncio
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import numpy as np
from decimal import Decimal

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_test_paper_trading_manager():
    """Create paper trading portfolio manager for testing"""
    
    try:
        from app.strategies.paper_trading_portfolio_manager import create_paper_trading_portfolio_manager
        from app.services.cost_calculator import CostCalculator
        from app.services.enhanced_slippage_estimator import EnhancedSlippageEstimator
        from app.services.mcp.wandb_cost_tracker import create_wandb_cost_tracker
        
        # Initialize cost calculator
        cost_calculator = CostCalculator()
        
        # Initialize slippage estimator
        slippage_estimator = EnhancedSlippageEstimator(
            redis_service=None,  # Will be set by paper trading manager
            config={
                "base_slippage_bps": 2.0,
                "size_impact_factor": 0.1,
                "volatility_factor": 0.05,
                "liquidity_factor": 0.03
            }
        )
        
        # Initialize W&B cost tracker
        wandb_cost_tracker = await create_wandb_cost_tracker(
            redis_url="redis://localhost:6379",
            cost_calculator=cost_calculator,
            slippage_estimator=slippage_estimator
        )
        
        # Create paper trading manager
        manager = await create_paper_trading_portfolio_manager(
            initial_balance_usd=100000.0,
            redis_url="redis://localhost:6379",
            cost_calculator=cost_calculator,
            slippage_estimator=slippage_estimator,
            wandb_cost_tracker=wandb_cost_tracker,
            config={
                "max_position_size_pct": 20.0,
                "slippage_simulation": True,
                "fee_simulation": True,
                "enable_cost_optimization": True,
                "enable_performance_tracking": True,
                "execution_latency_ms": 10,  # Reduced for testing
                "market_data_latency_ms": 5   # Reduced for testing
            }
        )
        
        return manager
        
    except Exception as e:
        logger.error(f"Failed to create paper trading manager: {e}")
        raise

def create_sample_market_data():
    """Create sample market data for testing"""
    
    from app.models.market_data import MarketData
    
    return MarketData(
        symbol="BTCUSDT",
        price=50000.0,
        volume=1000000.0,
        timestamp=datetime.now(),
        bid=49999.0,
        ask=50001.0,
        high_24h=51000.0,
        low_24h=49000.0,
        volatility=0.02
    )

async def test_paper_trading_portfolio_initialization(paper_trading_manager):
    """Test paper trading portfolio initialization"""
    
    logger.info("Testing paper trading portfolio initialization...")
    
    start_time = time.perf_counter()
    
    # Test portfolio summary
    summary = await paper_trading_manager.get_portfolio_summary()
    
    # Verify initial state
    assert summary["account"]["initial_balance"] == 100000.0
    assert summary["account"]["current_value"] == 100000.0
    assert summary["account"]["total_pnl"] == 0.0
    assert summary["account"]["pnl_percentage"] == 0.0
    
    # Verify initial balances
    assert "USD" in summary["balances"]
    assert summary["balances"]["USD"]["available"] == 100000.0
    assert summary["balances"]["USD"]["total"] == 100000.0
    
    # Verify empty positions
    assert len(summary["positions"]) == 0
    
    # Verify trading stats
    assert summary["trading_stats"]["total_trades"] == 0
    assert summary["trading_stats"]["win_rate"] == 0.0
    
    initialization_time = (time.perf_counter() - start_time) * 1000
    logger.info(f"Portfolio initialization completed in {initialization_time:.1f}ms")
    
    # Performance target: <50ms initialization
    assert initialization_time < 50, f"Initialization too slow: {initialization_time:.1f}ms"
    
    return True

async def test_paper_trade_execution_buy_order(paper_trading_manager, sample_market_data):
    """Test paper trade execution - BUY order"""
    
    logger.info("Testing paper trade execution - BUY order...")
    
    start_time = time.perf_counter()
    
    # Execute buy order
    order = await paper_trading_manager.execute_paper_trade(
        symbol="BTCUSDT",
        side="BUY",
        quantity=1.0,
        order_type="MARKET",
        market_data=sample_market_data
    )
    
    execution_time = (time.perf_counter() - start_time) * 1000
    logger.info(f"Buy order executed in {execution_time:.1f}ms")
    
    # Verify order execution
    assert order is not None, "Order should not be None"
    assert order.status == "FILLED", f"Order should be filled, got {order.status}"
    assert order.side == "BUY"
    assert order.symbol == "BTCUSDT"
    assert order.filled_quantity > 0
    assert order.filled_price > 0
    assert order.simulated_fees > 0
    
    # Check portfolio state after trade
    summary = await paper_trading_manager.get_portfolio_summary()
    
    # Verify BTC position created
    assert "BTC" in summary["positions"]
    btc_position = summary["positions"]["BTC"]
    assert btc_position["quantity"] == order.filled_quantity
    assert btc_position["avg_entry_price"] == order.filled_price
    
    # Verify USD balance reduced
    usd_balance = summary["balances"]["USD"]["available"]
    expected_cost = order.filled_quantity * order.filled_price + order.simulated_fees
    assert usd_balance < 100000.0
    assert abs((100000.0 - usd_balance) - expected_cost) < 0.01
    
    # Verify trading stats updated
    assert summary["trading_stats"]["total_trades"] == 1
    
    # Performance target: <100ms execution
    assert execution_time < 100, f"Execution too slow: {execution_time:.1f}ms"
    
    return True

async def test_paper_trade_execution_sell_order(paper_trading_manager, sample_market_data):
    """Test paper trade execution - SELL order"""
    
    logger.info("Testing paper trade execution - SELL order...")
    
    # First, execute a buy order to have something to sell
    buy_order = await paper_trading_manager.execute_paper_trade(
        symbol="BTCUSDT",
        side="BUY",
        quantity=1.0,
        order_type="MARKET",
        market_data=sample_market_data
    )
    
    assert buy_order is not None, "Buy order should succeed"
    
    # Now execute sell order
    start_time = time.perf_counter()
    
    sell_order = await paper_trading_manager.execute_paper_trade(
        symbol="BTCUSDT",
        side="SELL",
        quantity=0.5,  # Sell half
        order_type="MARKET",
        market_data=sample_market_data
    )
    
    execution_time = (time.perf_counter() - start_time) * 1000
    logger.info(f"Sell order executed in {execution_time:.1f}ms")
    
    # Verify sell order execution
    assert sell_order is not None, "Sell order should not be None"
    assert sell_order.status == "FILLED"
    assert sell_order.side == "SELL"
    assert sell_order.filled_quantity == 0.5
    
    # Check portfolio state after sell
    summary = await paper_trading_manager.get_portfolio_summary()
    
    # Verify BTC position reduced
    assert "BTC" in summary["positions"]
    btc_position = summary["positions"]["BTC"]
    assert abs(btc_position["quantity"] - 0.5) < 0.000001  # Remaining half
    
    # Verify trading stats updated
    assert summary["trading_stats"]["total_trades"] == 2
    
    # Performance target: <100ms execution
    assert execution_time < 100, f"Execution too slow: {execution_time:.1f}ms"
    
    return True

async def test_order_validation(paper_trading_manager, sample_market_data):
    """Test order validation rules"""
    
    logger.info("Testing order validation rules...")
    
    # Test insufficient balance
    insufficient_order = await paper_trading_manager.execute_paper_trade(
        symbol="BTCUSDT",
        side="BUY",
        quantity=10.0,  # $500k worth, exceeds $100k balance
        order_type="MARKET",
        market_data=sample_market_data
    )
    
    assert insufficient_order is None, "Order with insufficient balance should fail"
    
    # Test selling non-existent position
    sell_nonexistent = await paper_trading_manager.execute_paper_trade(
        symbol="ETHUSDT",
        side="SELL",
        quantity=1.0,
        order_type="MARKET",
        market_data=sample_market_data
    )
    
    assert sell_nonexistent is None, "Selling non-existent position should fail"
    
    logger.info("Order validation tests passed")
    return True

async def test_cost_optimization_integration(paper_trading_manager, sample_market_data):
    """Test cost optimization integration"""
    
    logger.info("Testing cost optimization integration...")
    
    # Execute trade with cost optimization enabled
    order = await paper_trading_manager.execute_paper_trade(
        symbol="BTCUSDT",
        side="BUY",
        quantity=1.0,
        order_type="MARKET",
        market_data=sample_market_data
    )
    
    assert order is not None, "Order should execute successfully"
    
    # Verify cost optimization tracking
    assert hasattr(order, 'cost_optimization_used')
    assert order.simulated_fees > 0, "Should have realistic fees"
    assert order.simulated_slippage >= 0, "Should have slippage calculation"
    
    # Check if cost savings are tracked
    summary = await paper_trading_manager.get_portfolio_summary()
    cost_savings = summary["trading_stats"]["cost_savings"]
    
    # Cost savings should be non-negative
    assert cost_savings >= 0, f"Cost savings should be non-negative: {cost_savings}"
    
    logger.info(f"Cost optimization integration verified with savings: ${cost_savings:.4f}")
    return True

async def test_performance_tracking_integration(paper_trading_manager, sample_market_data):
    """Test W&B performance tracking integration"""
    
    logger.info("Testing W&B performance tracking integration...")
    
    # Execute multiple trades to generate performance data
    trades = []
    
    for i in range(3):
        order = await paper_trading_manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="BUY",
            quantity=0.1,
            order_type="MARKET",
            market_data=sample_market_data
        )
        
        if order:
            trades.append(order)
            
        # Small delay between trades
        await asyncio.sleep(0.1)
    
    assert len(trades) >= 2, "Should execute at least 2 trades for performance tracking"
    
    # Check performance history
    assert len(paper_trading_manager.performance_history) > 0, "Should have performance history"
    
    # Verify performance metrics calculation
    summary = await paper_trading_manager.get_portfolio_summary()
    stats = summary["trading_stats"]
    
    assert stats["total_trades"] >= 2
    assert stats["total_fees_paid"] > 0
    assert stats["win_rate"] >= 0
    assert stats["sharpe_ratio"] >= 0 or stats["sharpe_ratio"] <= 0  # Can be negative
    
    logger.info("Performance tracking integration verified")
    return True

async def test_portfolio_value_calculation(paper_trading_manager, sample_market_data):
    """Test portfolio value calculation accuracy"""
    
    logger.info("Testing portfolio value calculation...")
    
    # Get initial portfolio value
    initial_summary = await paper_trading_manager.get_portfolio_summary()
    initial_value = initial_summary["account"]["current_value"]
    
    assert initial_value == 100000.0, f"Initial value should be $100k, got ${initial_value}"
    
    # Execute trade
    order = await paper_trading_manager.execute_paper_trade(
        symbol="BTCUSDT",
        side="BUY",
        quantity=1.0,
        order_type="MARKET",
        market_data=sample_market_data
    )
    
    assert order is not None
    
    # Get portfolio value after trade
    post_trade_summary = await paper_trading_manager.get_portfolio_summary()
    post_trade_value = post_trade_summary["account"]["current_value"]
    
    # Portfolio value should be approximately the same (minus fees)
    expected_value = initial_value - order.simulated_fees
    value_diff = abs(post_trade_value - expected_value)
    
    assert value_diff < 10.0, f"Portfolio value calculation error: {value_diff:.2f}"
    
    # Check unrealized PnL calculation
    btc_position = post_trade_summary["positions"]["BTC"]
    unrealized_pnl = btc_position["unrealized_pnl"]
    
    # Should be close to zero for immediate execution
    assert abs(unrealized_pnl) < 100, f"Unrealized PnL too large: ${unrealized_pnl:.2f}"
    
    logger.info("Portfolio value calculation verified")
    return True

async def test_system_reliability_and_performance(paper_trading_manager, sample_market_data):
    """Test system reliability and performance under load"""
    
    logger.info("Testing system reliability and performance under load...")
    
    execution_times = []
    success_count = 0
    total_attempts = 10  # Reduced for faster testing
    
    start_time = time.perf_counter()
    
    # Execute multiple trades to test reliability
    for i in range(total_attempts):
        trade_start = time.perf_counter()
        
        order = await paper_trading_manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="BUY" if i % 2 == 0 else "SELL",
            quantity=0.01,  # Small orders
            order_type="MARKET",
            market_data=sample_market_data
        )
        
        trade_time = (time.perf_counter() - trade_start) * 1000
        execution_times.append(trade_time)
        
        if order and order.status == "FILLED":
            success_count += 1
        
        # Small delay to simulate realistic trading
        await asyncio.sleep(0.05)
    
    total_time = (time.perf_counter() - start_time) * 1000
    
    # Analyze performance metrics
    avg_execution_time = np.mean(execution_times)
    max_execution_time = np.max(execution_times)
    p95_execution_time = np.percentile(execution_times, 95)
    success_rate = (success_count / total_attempts) * 100
    
    logger.info(f"Performance Results:")
    logger.info(f"  Total Trades: {total_attempts}")
    logger.info(f"  Success Rate: {success_rate:.1f}%")
    logger.info(f"  Avg Execution Time: {avg_execution_time:.1f}ms")
    logger.info(f"  Max Execution Time: {max_execution_time:.1f}ms")
    logger.info(f"  P95 Execution Time: {p95_execution_time:.1f}ms")
    logger.info(f"  Total Test Time: {total_time:.1f}ms")
    
    # Performance assertions
    assert success_rate >= 50, f"Success rate too low: {success_rate:.1f}%"  # Relaxed for testing
    assert avg_execution_time < 200, f"Average execution time too slow: {avg_execution_time:.1f}ms"  # Relaxed
    assert p95_execution_time < 500, f"P95 execution time too slow: {p95_execution_time:.1f}ms"  # Relaxed
    
    logger.info("System reliability and performance test passed")
    return True

async def test_portfolio_reset_functionality(paper_trading_manager, sample_market_data):
    """Test portfolio reset functionality"""
    
    logger.info("Testing portfolio reset functionality...")
    
    # Execute some trades
    for i in range(2):  # Reduced for faster testing
        await paper_trading_manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="BUY",
            quantity=0.1,
            order_type="MARKET",
            market_data=sample_market_data
        )
    
    # Verify trades executed
    summary_before = await paper_trading_manager.get_portfolio_summary()
    
    # Reset portfolio
    await paper_trading_manager.reset_portfolio(new_initial_balance=150000.0)
    
    # Verify reset
    summary_after = await paper_trading_manager.get_portfolio_summary()
    
    assert summary_after["account"]["initial_balance"] == 150000.0
    assert summary_after["account"]["current_value"] == 150000.0
    assert summary_after["account"]["total_pnl"] == 0.0
    assert summary_after["trading_stats"]["total_trades"] == 0
    assert len(summary_after["positions"]) == 0
    assert summary_after["balances"]["USD"]["available"] == 150000.0
    
    logger.info("Portfolio reset functionality verified")
    return True

async def test_end_to_end_paper_trading_workflow(paper_trading_manager, sample_market_data):
    """Test complete end-to-end paper trading workflow"""
    
    logger.info("Testing end-to-end paper trading workflow...")
    
    workflow_start = time.perf_counter()
    
    # 1. Initial portfolio check
    initial_summary = await paper_trading_manager.get_portfolio_summary()
    assert initial_summary["account"]["current_value"] == 150000.0  # From previous reset
    
    # 2. Execute buy orders
    buy_orders = []
    for i in range(2):  # Reduced for faster testing
        order = await paper_trading_manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="BUY",
            quantity=0.5,
            order_type="MARKET",
            market_data=sample_market_data
        )
        if order:
            buy_orders.append(order)
    
    assert len(buy_orders) >= 1, "Should execute at least 1 buy order"
    
    # 3. Check portfolio state after buys
    mid_summary = await paper_trading_manager.get_portfolio_summary()
    assert "BTC" in mid_summary["positions"]
    assert mid_summary["balances"]["USD"]["available"] < 150000.0
    
    # 4. Execute sell orders
    sell_orders = []
    for i in range(1):
        order = await paper_trading_manager.execute_paper_trade(
            symbol="BTCUSDT",
            side="SELL",
            quantity=0.3,
            order_type="MARKET",
            market_data=sample_market_data
        )
        if order:
            sell_orders.append(order)
    
    assert len(sell_orders) >= 1, "Should execute at least 1 sell order"
    
    # 5. Final portfolio check
    final_summary = await paper_trading_manager.get_portfolio_summary()
    
    # Verify workflow completion
    total_trades = final_summary["trading_stats"]["total_trades"]
    assert total_trades >= 3, f"Should have at least 3 trades, got {total_trades}"
    
    # Verify P&L calculation
    total_pnl = final_summary["account"]["total_pnl"]
    assert isinstance(total_pnl, (int, float)), "P&L should be numeric"
    
    # Verify fee tracking
    total_fees = final_summary["trading_stats"]["total_fees_paid"]
    assert total_fees > 0, "Should have paid some fees"
    
    # Verify portfolio value consistency
    current_value = final_summary["account"]["current_value"]
    assert current_value > 0, "Portfolio value should be positive"
    
    workflow_time = (time.perf_counter() - workflow_start) * 1000
    
    logger.info(f"End-to-end workflow completed in {workflow_time:.1f}ms")
    logger.info(f"  Total Trades: {total_trades}")
    logger.info(f"  Final Portfolio Value: ${current_value:,.2f}")
    logger.info(f"  Total P&L: ${total_pnl:+,.2f}")
    logger.info(f"  Total Fees: ${total_fees:.4f}")
    
    # Performance target: Complete workflow in <5 seconds
    assert workflow_time < 5000, f"Workflow too slow: {workflow_time:.1f}ms"
    
    logger.info("End-to-end paper trading workflow test passed")
    return True

# Main test runner
async def run_paper_trading_tests():
    """Run paper trading tests standalone"""
    
    logger.info("Starting Paper Trading Environment Tests (Task 3.2.1)")
    logger.info("=" * 60)
    
    try:
        # Initialize test environment
        logger.info("Initializing test environment...")
        
        # Create paper trading manager
        paper_trading_manager = await create_test_paper_trading_manager()
        
        # Create sample market data
        sample_market_data = create_sample_market_data()
        
        # Run tests
        tests = [
            ("Portfolio Initialization", test_paper_trading_portfolio_initialization, [paper_trading_manager]),
            ("Buy Order Execution", test_paper_trade_execution_buy_order, [paper_trading_manager, sample_market_data]),
            ("Sell Order Execution", test_paper_trade_execution_sell_order, [paper_trading_manager, sample_market_data]),
            ("Order Validation", test_order_validation, [paper_trading_manager, sample_market_data]),
            ("Cost Optimization Integration", test_cost_optimization_integration, [paper_trading_manager, sample_market_data]),
            ("Performance Tracking Integration", test_performance_tracking_integration, [paper_trading_manager, sample_market_data]),
            ("Portfolio Value Calculation", test_portfolio_value_calculation, [paper_trading_manager, sample_market_data]),
            ("System Reliability & Performance", test_system_reliability_and_performance, [paper_trading_manager, sample_market_data]),
            ("Portfolio Reset Functionality", test_portfolio_reset_functionality, [paper_trading_manager, sample_market_data]),
            ("End-to-End Workflow", test_end_to_end_paper_trading_workflow, [paper_trading_manager, sample_market_data])
        ]
        
        passed_tests = 0
        failed_tests = 0
        
        for test_name, test_func, test_args in tests:
            try:
                logger.info(f"\nRunning: {test_name}")
                logger.info("-" * 40)
                
                start_time = time.perf_counter()
                
                result = await test_func(*test_args)
                
                test_time = (time.perf_counter() - start_time) * 1000
                logger.info(f"✅ {test_name} PASSED ({test_time:.1f}ms)")
                passed_tests += 1
                
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED: {e}")
                failed_tests += 1
                continue
        
        # Test summary
        logger.info("\n" + "=" * 60)
        logger.info("PAPER TRADING TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"Tests Passed: {passed_tests}")
        logger.info(f"Tests Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests / (passed_tests + failed_tests)) * 100:.1f}%")
        
        if failed_tests == 0:
            logger.info("🎉 ALL TESTS PASSED - Paper Trading Environment Ready for Deployment!")
        else:
            logger.warning(f"⚠️  {failed_tests} tests failed - Review before deployment")
        
        return failed_tests == 0
        
    except Exception as e:
        logger.error(f"Test setup failed: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    # Run tests
    success = asyncio.run(run_paper_trading_tests())
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)