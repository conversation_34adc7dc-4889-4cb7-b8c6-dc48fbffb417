# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Python/FastAPI)
```bash
# Start main trading engine
python main.py

# Start dashboard API server
python -m app.dashboard.main

# Run tests
pytest

# Run tests with coverage
pytest --cov=app tests/

# Run specific test file
pytest tests/test_file.py
```

### Frontend (React/TypeScript)
```bash
# Navigate to frontend directory
cd app/dashboard/frontend

# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build

# Run frontend tests
npm test
```

### Full Application Startup
```bash
# Terminal 1: Start backend API
python -m app.dashboard.main

# Terminal 2: Start frontend (from app/dashboard/frontend)
npm start

# Access dashboard at http://localhost:3000
```

## High-Level Architecture

### Core Application Structure
The system uses a **microservices-style architecture** with two main entry points:
- **Main Trading Engine** (`main.py`) - Core trading logic and strategy execution
- **Dashboard API** (`app/dashboard/main.py`) - Web interface and monitoring

### Key Service Layers

#### Exchange Integration Layer
- `app/services/exchange/binance_client.py` - Primary Binance API interface
- `app/services/exchange/exchange_client.py` - Abstract exchange interface
- Handles market data, order placement, account management

#### Execution Service Layer
- `app/services/execution/execution_service.py` - Main trade execution orchestrator
- Modular design with mixins for lifecycle, order placement, trade operations
- `app/services/execution/order_manager.py` - Order lifecycle management
- `app/services/execution/trade_management.py` - Position and trade tracking

#### Strategy Layer
- `app/strategies/strategy_selector.py` - Main strategy orchestration engine
- Three core strategies:
  - **GridStrategy** - Range-bound market trading
  - **TechnicalAnalysisStrategy** - RSI/MACD/Bollinger Bands trading
  - **TrendFollowingStrategy** - EMA crossover and momentum trading

#### ML/AI Layer
- `app/ml/models/weight_optimizer.py` - Reinforcement learning for strategy optimization
- Uses PPO (Proximal Policy Optimization) for strategy weight learning
- `app/ml/data_collector.py` - Training data collection and preparation
- `app/ml/feature_engineering.py` - Market feature extraction

### Data Flow Architecture
```
Market Data → Exchange Client → Strategy Selector → Strategy Selection → Execution Service → Orders
     ↓                                    ↓
ML Feature Engineering ←→ ML Weight Optimizer → Strategy Weights
     ↓                                    ↓
WebSocket Updates → Dashboard API → Frontend UI
```

### Strategy Selection Process
1. **Market Analysis**: Calculate volatility, trend strength, range-bound conditions, volume
2. **Strategy Scoring**: Each strategy scores its suitability (0-1) for current market conditions
3. **ML Enhancement**: Optional reinforcement learning model provides learned strategy weights
4. **Strategy Selection**: Best strategy selected based on scores/ML weights
5. **Trade Execution**: Selected strategy generates signals and executes trades

### Frontend Architecture
- **React/TypeScript** with Material-UI components
- **Authentication**: JWT-based with AuthContext
- **Real-time Updates**: WebSocket integration for live data
- **Main Components**:
  - `TradeDashboard.tsx` - Real-time monitoring and manual controls
  - `MLOptimization.tsx` - Model training and evaluation interface
  - `AccountStatistics.tsx` - Performance metrics and account status

## Development Guidelines

### Project Structure Conventions
- **Services**: Business logic and external integrations in `app/services/`
- **Models**: Data models and schemas in `app/models/`
- **Strategies**: Trading strategy implementations in `app/strategies/`
- **ML Components**: Machine learning models and training in `app/ml/`
- **API Routes**: Dashboard API endpoints in `app/dashboard/`
- **Frontend**: React components in `app/dashboard/frontend/src/`

### Testing Strategy
- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test service interactions
- **ML Tests**: Test machine learning model training and evaluation
- All tests in `tests/` directory, mirroring the `app/` structure

### Configuration Management
- Environment variables defined in `.env` file
- Settings centralized in `app/config/settings.py`
- Machine learning parameters in `app/config/app_config.py`

### Key Development Patterns
- **Dependency Injection**: Services injected via FastAPI dependency system
- **Strategy Pattern**: Pluggable trading strategies with common interface
- **Observer Pattern**: WebSocket handlers for real-time updates
- **ML Integration**: Optional reinforcement learning enhancement for strategy selection

### Important Implementation Notes
- **Exchange Client**: Abstracted to support multiple exchanges (currently Binance)
- **Strategy Switching**: Includes cooldown periods and position closure
- **Risk Management**: Built-in position sizing, stop-loss, and take-profit mechanisms
- **ML Model Persistence**: Models saved to `models/` directory with joblib
- **WebSocket Handling**: Real-time market data and order updates via WebSocket connections