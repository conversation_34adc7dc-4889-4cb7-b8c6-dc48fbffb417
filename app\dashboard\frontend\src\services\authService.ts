/**
 * Authentication Service
 *
 * This service handles authentication with the backend API.
 */
import axios from 'axios';
import tokenService from './tokenService';

// API URL from environment variable
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

/**
 * Login with username and password
 *
 * @param username Username
 * @param password Password
 * @returns Promise with login result
 */
export const login = async (username: string, password: string): Promise<boolean> => {
  try {
    // Create form data for login
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    // Send login request
    const response = await axios.post(`${API_URL}/api/token`, formData);

    // Save tokens to local storage
    const { access_token, refresh_token, token_type } = response.data;
    tokenService.saveTokens(access_token, refresh_token, token_type);

    return true;
  } catch (error) {
    console.error('Login error:', error);
    return false;
  }
};

/**
 * Logout the user
 */
export const logout = (): void => {
  tokenService.clearTokens();
};

/**
 * Refresh the access token using the refresh token
 *
 * @returns Promise with refresh result
 */
export const refreshToken = async (): Promise<boolean> => {
  try {
    // Get refresh token from local storage
    const refreshToken = tokenService.getRefreshToken();
    if (!refreshToken) {
      return false;
    }

    // Check if refresh token is expired
    if (tokenService.isRefreshTokenExpired()) {
      tokenService.clearTokens();
      return false;
    }

    // Send refresh token request
    const response = await axios.post(`${API_URL}/api/refresh-token`, { refresh_token: refreshToken });

    // Save new tokens to local storage
    const { access_token, refresh_token, token_type } = response.data;
    tokenService.saveTokens(access_token, refresh_token, token_type);

    return true;
  } catch (error) {
    console.error('Token refresh error:', error);
    tokenService.clearTokens();
    return false;
  }
};

export default {
  login,
  logout,
  refreshToken,
};
